import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_zone_models.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/new_item/utils/zone_configuration_processor.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Custom painter that shows the full pattern with zones overlaid
/// and tracks progress within the current active zone
class ZoneAwareProgressPainter extends CustomPainter {
  final List<ShapeData> shapes;
  final List<List<bool>> fullPatternInstructions;
  final List<KnittingZone> zones;
  final int currentZoneIndex;
  final int currentRow;
  final double progressPercentage;
  final BuildContext? context;
  final int topStitches;
  final int bottomStitches;
  final String topWidth;
  final String bottomWidth;
  final String height;
  final int rowsCount;
  final int leftNeedles;
  final int rightNeedles;
  final double stitchesPerCm;
  final double rowsPerCm;
  final bool showProgress;
  final Map<String, dynamic>? zonePositionInfo;
  final List<bool> completedZones; // Track which zones are completed
  final bool isPatternCompleted; // Track if entire pattern is completed

  // Performance optimization: Cache expensive computations
  static final Map<String, ui.Picture> _patternCache = {};
  static final Map<String, List<List<bool>>> _processedZoneCache = {};

  // Cache for paint objects to avoid recreation
  late final Paint _fullPatternPaint;
  late final Paint _outlinePaint;
  late final Paint _progressPaint;
  late final Paint _progressOutlinePaint;

  ZoneAwareProgressPainter({
    this.shapes = const [],
    required this.fullPatternInstructions,
    required this.zones,
    required this.currentZoneIndex,
    required this.currentRow,
    required this.progressPercentage,
    required this.topStitches,
    required this.bottomStitches,
    required this.topWidth,
    required this.bottomWidth,
    required this.height,
    required this.rowsCount,
    required this.leftNeedles,
    required this.rightNeedles,
    required this.stitchesPerCm,
    required this.rowsPerCm,
    this.showProgress = true,
    this.context,
    this.zonePositionInfo,
    this.completedZones = const [], // Default to empty list
    this.isPatternCompleted = false, // Default to not completed
    int? totalRows,
  }) {
    // Initialize cached paint objects for better performance
    _initializePaintObjects();
  }

  /// Initialize paint objects once to avoid recreation during painting
  void _initializePaintObjects() {
    _fullPatternPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    _outlinePaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.5)
      ..strokeWidth = 0.7
      ..style = PaintingStyle.stroke;

    _zonePaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.6)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    _progressPaint = Paint()
      ..color = getProgressColor().withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;

    _progressOutlinePaint = Paint()
      ..color = getProgressPillColor().withValues(alpha: 0.7)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Performance optimization: Use viewport culling and efficient rendering
    final scalingInfo = _calculateScalingInfo(Size(
      math.max(0, size.width - 0.2),
      math.max(0, size.height - 0.2),
    ));

    // Calculate visible area for culling
    final visibleRect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Draw the full pattern as a background with optimized rendering
    _drawFullPatternOptimized(canvas, size, scalingInfo, visibleRect);

    // Draw completed zones first (behind current zone)
    _drawCompletedZonesOptimized(canvas, size, scalingInfo, visibleRect);

    // Draw all zone boundaries
    _drawAllZones(canvas, size);

    // Highlight the current active zone
    _drawActiveZone(canvas, size);

    // Draw progress fill on the active zone with optimization
    if (showProgress && currentZoneIndex < zones.length) {
      _drawZoneProgressOptimized(canvas, size, scalingInfo, visibleRect);
    }

    // Draw dimension indicators
    _drawDimensionIndicators(canvas, size);

    if (showProgress) {
      // Draw horizontal indicator line for current row
      _drawHorizontalRowLine(canvas, size);

      // Draw the current row indicator text
      _drawCurrentRowIndicator(canvas, size);

      // Draw the progress percentage in the top-right corner
      _drawProgressPercentageInTopRight(canvas, size);
    }

    // Draw pattern completion overlay if entire pattern is completed
    if (isPatternCompleted) {
      _drawPatternCompletionOverlay(canvas, size);
    }
  }

  // Get theme-aware colors
  Color getPrimaryColor() {
    return context != null
        ? Theme.of(context!).colorScheme.primary
        : Get.theme.colorScheme.primary;
  }

  Color getProgressColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary.withValues(alpha: 0.3)
        : Colors.green.withValues(alpha: 0.3);
  }

  Color getProgressPillColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary
        : Colors.green;
  }

  Color getCompletedZoneColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary.withValues(alpha: 0.4)
        : Colors.green.withValues(alpha: 0.4);
  }

  Color getCompletedZoneOutlineColor() {
    return context != null
        ? Theme.of(context!).colorScheme.tertiary
        : Colors.green;
  }

  Color getPatternCompletionColor() {
    return context != null
        ? Colors.amber.withValues(alpha: 0.2)
        : Colors.amber.withValues(alpha: 0.2);
  }

  Color getLineColor() {
    return context != null
        ? Theme.of(context!).colorScheme.onSurface.withValues(alpha: 0.5)
        : Colors.grey;
  }

  Color getBackgroundColor() {
    return context != null
        ? Theme.of(context!).colorScheme.surface
        : Colors.white;
  }

  Color getTextColor() {
    return context != null
        ? Theme.of(context!).colorScheme.onSurface.withValues(alpha: 0.7)
        : Colors.grey[600]!;
  }

  /// Helper method to calculate scaling info based on the full pattern
  ({
    Size scaledSize,
    double scaleX,
    double scaleY,
    double offsetX,
    double offsetY,
    int minStitch,
    int maxStitch,
    int patternWidth
  }) _calculateScalingInfo(Size availableSize) {
    if (fullPatternInstructions.isEmpty) {
      return (
        scaledSize: Size.zero,
        scaleX: 0,
        scaleY: 0,
        offsetX: 0,
        offsetY: 0,
        minStitch: 0,
        maxStitch: 0,
        patternWidth: 0
      );
    }

    // For full pattern, analyze the entire width
    int minStitch = 0;
    int maxStitch = fullPatternInstructions.first.length - 1;
    int patternWidth = maxStitch - minStitch + 1;
    int patternHeight = fullPatternInstructions.length;

    // Calculate the PHYSICAL aspect ratio based on gauge
    final double safeStitchesPerCm = math.max(0.01, stitchesPerCm);
    final double safeRowsPerCm = math.max(0.01, rowsPerCm);
    final double physicalWidth = patternWidth / safeStitchesPerCm;
    final double physicalHeight = patternHeight / safeRowsPerCm;
    final double physicalAspectRatio = (physicalHeight > 1e-6)
        ? physicalWidth / physicalHeight
        : 1.0; // Avoid division by zero

    // MAXIMIZE space utilization - use nearly all available space
    const double scalingBoost =
        0.98; // Use 98% of available space for maximum size
    double scaledWidth;
    double scaledHeight;

    if (availableSize.width / availableSize.height > physicalAspectRatio) {
      // Available space is wider than the pattern aspect ratio
      // Constrain by height and calculate width
      scaledHeight = availableSize.height * scalingBoost;
      scaledWidth = scaledHeight * physicalAspectRatio;
    } else {
      // Available space is taller than the pattern aspect ratio
      // Constrain by width and calculate height
      scaledWidth = availableSize.width * scalingBoost;
      scaledHeight = (physicalAspectRatio > 1e-6)
          ? scaledWidth / physicalAspectRatio
          : availableSize.height * scalingBoost; // Avoid division by zero
    }

    // Use maximum possible space without any artificial constraints
    final double finalScaledWidth = scaledWidth.toDouble();
    final double finalScaledHeight = scaledHeight.toDouble();
    final Size finalScaledSize = Size(finalScaledWidth, finalScaledHeight);

    // Calculate scale factors
    final double scaleX =
        (patternWidth > 0) ? finalScaledWidth / patternWidth : 0.0;
    final double scaleY =
        (patternHeight > 0) ? finalScaledHeight / patternHeight : 0.0;

    // Use minimal padding for absolute maximum space utilization
    const double internalPadding = 0.1; // Nearly zero padding for maximum size
    final double offsetX =
        internalPadding + (availableSize.width - finalScaledWidth) / 2.0;
    final double offsetY =
        internalPadding + (availableSize.height - finalScaledHeight) / 2.0;

    return (
      scaledSize: finalScaledSize,
      scaleX: scaleX,
      scaleY: scaleY,
      offsetX: offsetX,
      offsetY: offsetY,
      minStitch: minStitch,
      maxStitch: maxStitch,
      patternWidth: patternWidth
    );
  }

  /// Optimized full pattern drawing with viewport culling and batch rendering
  void _drawFullPatternOptimized(
      Canvas canvas, Size size, dynamic scalingInfo, Rect visibleRect) {
    if (fullPatternInstructions.isEmpty) return;

    // Performance optimization: Calculate level of detail based on cell size
    final cellSize = math.min<double>(
        scalingInfo.scaleX.toDouble(), scalingInfo.scaleY.toDouble());
    final shouldDrawOutlines =
        cellSize > 2.0; // Only draw outlines if cells are large enough
    final shouldDrawIndividualCells =
        cellSize > 0.5; // Use batch rendering for very small cells

    if (!shouldDrawIndividualCells) {
      // For very small cells, use a single filled rectangle for the entire pattern
      _drawPatternAsBlock(canvas, scalingInfo);
      return;
    }

    // Calculate visible row and column ranges for culling
    final visibleRowStart = math.max(0,
        ((visibleRect.top - scalingInfo.offsetY) / scalingInfo.scaleY).floor());
    final visibleRowEnd = math.min(
        fullPatternInstructions.length,
        ((visibleRect.bottom - scalingInfo.offsetY) / scalingInfo.scaleY)
                .ceil() +
            1);
    final visibleColStart = math.max(
        0,
        ((visibleRect.left - scalingInfo.offsetX) / scalingInfo.scaleX)
            .floor());
    final visibleColEnd = math.min(
        fullPatternInstructions.isNotEmpty
            ? fullPatternInstructions[0].length
            : 0,
        ((visibleRect.right - scalingInfo.offsetX) / scalingInfo.scaleX)
                .ceil() +
            1);

    // Batch rectangles for efficient drawing
    final List<Rect> fillRects = [];
    final List<Rect> outlineRects = [];

    // Only process visible cells
    for (int rowIndex = visibleRowStart; rowIndex < visibleRowEnd; rowIndex++) {
      if (rowIndex >= fullPatternInstructions.length) break;
      final row = fullPatternInstructions[rowIndex];

      for (int colIndex = visibleColStart;
          colIndex < visibleColEnd;
          colIndex++) {
        if (colIndex >= row.length) break;

        if (row[colIndex]) {
          final cellLeft = scalingInfo.offsetX + colIndex * scalingInfo.scaleX;
          final cellTop = scalingInfo.offsetY + rowIndex * scalingInfo.scaleY;

          final cellMargin = shouldDrawOutlines ? cellSize * 0.05 : 0.0;
          final cellRect = Rect.fromLTWH(
              cellLeft + cellMargin,
              cellTop + cellMargin,
              scalingInfo.scaleX - cellMargin * 2,
              scalingInfo.scaleY - cellMargin * 2);

          fillRects.add(cellRect);
          if (shouldDrawOutlines) {
            outlineRects.add(cellRect);
          }
        }
      }
    }

    // Batch draw all rectangles
    _batchDrawRects(canvas, fillRects, _fullPatternPaint);
    if (shouldDrawOutlines && outlineRects.isNotEmpty) {
      _batchDrawRects(canvas, outlineRects, _outlinePaint);
    }
  }

  /// Draw pattern as a single block for very small zoom levels
  void _drawPatternAsBlock(Canvas canvas, dynamic scalingInfo) {
    final patternRect = Rect.fromLTWH(
      scalingInfo.offsetX,
      scalingInfo.offsetY,
      scalingInfo.scaledSize.width,
      scalingInfo.scaledSize.height,
    );
    canvas.drawRect(patternRect, _fullPatternPaint);
  }

  /// Efficiently draw multiple rectangles in batches
  void _batchDrawRects(Canvas canvas, List<Rect> rects, Paint paint) {
    if (rects.isEmpty) return;

    // For large numbers of rectangles, use path for better performance
    if (rects.length > 100) {
      final path = ui.Path();
      for (final rect in rects) {
        path.addRect(rect);
      }
      canvas.drawPath(path, paint);
    } else {
      // For smaller numbers, individual draws are fine
      for (final rect in rects) {
        canvas.drawRect(rect, paint);
      }
    }
  }

  /// Draw outlines for all zones
  void _drawAllZones(Canvas canvas, Size size) {
    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    // Zone outline paint - increased opacity for better visibility
    final zonePaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.6)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw each zone boundary
    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      final zone = zones[zoneIndex];

      // Skip the current zone as it will be highlighted separately
      if (zoneIndex == currentZoneIndex) continue;

      // Calculate zone boundaries
      final zoneLeft =
          scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX;
      final zoneTop = scalingInfo.offsetY + zone.startRow * scalingInfo.scaleY;
      final zoneWidth =
          (zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX;
      final zoneHeight = (zone.endRow - zone.startRow + 1) * scalingInfo.scaleY;

      final zoneRect = Rect.fromLTWH(zoneLeft, zoneTop, zoneWidth, zoneHeight);

      // Draw zone boundary
      canvas.drawRect(zoneRect, zonePaint);

      // Draw zone name
      _drawTextInZone(
          canvas,
          "${zoneIndex + 1}",
          Offset(zoneLeft + zoneWidth / 2, zoneTop + zoneHeight / 2),
          Colors.grey.withValues(alpha: 0.8));
    }
  }

  /// Highlight the current active zone
  void _drawActiveZone(Canvas canvas, Size size) {
    if (currentZoneIndex < 0 || currentZoneIndex >= zones.length) return;

    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    final activeZone = zones[currentZoneIndex];

    // Calculate zone boundaries
    final zoneLeft =
        scalingInfo.offsetX + activeZone.startNeedle * scalingInfo.scaleX;
    final zoneTop =
        scalingInfo.offsetY + activeZone.startRow * scalingInfo.scaleY;
    final zoneWidth = (activeZone.endNeedle - activeZone.startNeedle + 1) *
        scalingInfo.scaleX;
    final zoneHeight =
        (activeZone.endRow - activeZone.startRow + 1) * scalingInfo.scaleY;

    final zoneRect = Rect.fromLTWH(zoneLeft, zoneTop, zoneWidth, zoneHeight);

    // Add a more visible background tint to active zone
    final zoneTintPaint = Paint()
      ..color = getPrimaryColor().withValues(alpha: 0.1) // Increased from 0.05
      ..style = PaintingStyle.fill;

    canvas.drawRect(zoneRect, zoneTintPaint);

    // Draw active zone with a more distinctive border
    final activeZonePaint = Paint()
      ..color = getPrimaryColor()
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    // Draw a dashed outline for more distinctiveness
    _drawDashedRect(canvas, zoneRect, activeZonePaint);

    // Draw zone name with enhanced visibility
    _drawTextInZone(canvas, "${currentZoneIndex + 1}",
        Offset(zoneLeft + zoneWidth / 2, zoneTop + 20), getPrimaryColor(),
        fontSize: 10.0 // Increased from 14.0
        );
  }

  /// Optimized zone progress drawing with caching and viewport culling
  void _drawZoneProgressOptimized(
      Canvas canvas, Size size, dynamic scalingInfo, Rect visibleRect) {
    if (currentZoneIndex < 0 || currentZoneIndex >= zones.length) return;

    final activeZone = zones[currentZoneIndex];

    // Cache processed instructions to avoid repeated computation
    final cacheKey = '${activeZone.hashCode}_${activeZone.instructions.length}';
    List<List<bool>> processedInstructions;

    if (_processedZoneCache.containsKey(cacheKey)) {
      processedInstructions = _processedZoneCache[cacheKey]!;
    } else {
      processedInstructions =
          ZoneConfigurationProcessor.applyZoneConfiguration(activeZone);
      _processedZoneCache[cacheKey] = processedInstructions;
    }

    if (processedInstructions.isEmpty) return;

    // Calculate which rows to include based on progress percentage
    final rowsToInclude =
        (processedInstructions.length * progressPercentage).round();
    if (rowsToInclude <= 0) return;

    // Performance optimization: Calculate level of detail
    final cellSize = math.min<double>(
        scalingInfo.scaleX.toDouble(), scalingInfo.scaleY.toDouble());
    final shouldDrawOutlines = cellSize > 2.0;

    // Batch rectangles for efficient drawing
    final List<Rect> progressRects = [];
    final List<Rect> outlineRects = [];

    // Only process rows that need to be drawn
    final startRowIndex =
        math.max(0, processedInstructions.length - rowsToInclude);

    for (int rowIndex = startRowIndex;
        rowIndex < processedInstructions.length;
        rowIndex++) {
      final fullPatternRowIndex = activeZone.startRow + rowIndex;

      // Viewport culling: Skip rows outside visible area
      final rowTop =
          scalingInfo.offsetY + fullPatternRowIndex * scalingInfo.scaleY;
      final rowBottom = rowTop + scalingInfo.scaleY;
      if (rowBottom < visibleRect.top || rowTop > visibleRect.bottom) continue;

      final row = processedInstructions[rowIndex];
      for (int colIndex = 0; colIndex < row.length; colIndex++) {
        if (row[colIndex]) {
          final centerOffset =
              (row.length - activeZone.instructions[rowIndex].length) ~/ 2;
          final fullPatternColIndex =
              activeZone.startNeedle + colIndex - centerOffset;

          // Skip rendering if outside the full pattern boundaries
          if (fullPatternColIndex < 0 ||
              fullPatternColIndex >= fullPatternInstructions[0].length) {
            continue;
          }

          // Viewport culling: Skip columns outside visible area
          final cellLeft =
              scalingInfo.offsetX + fullPatternColIndex * scalingInfo.scaleX;
          final cellRight = cellLeft + scalingInfo.scaleX;
          if (cellRight < visibleRect.left || cellLeft > visibleRect.right)
            continue;

          final cellTop =
              scalingInfo.offsetY + fullPatternRowIndex * scalingInfo.scaleY;
          final cellMargin = shouldDrawOutlines ? cellSize * 0.05 : 0.0;

          final cellRect = Rect.fromLTWH(
              cellLeft + cellMargin,
              cellTop + cellMargin,
              scalingInfo.scaleX - cellMargin * 2,
              scalingInfo.scaleY - cellMargin * 2);

          progressRects.add(cellRect);
          if (shouldDrawOutlines) {
            outlineRects.add(cellRect);
          }
        }
      }
    }

    // Batch draw all progress rectangles
    _batchDrawRects(canvas, progressRects, _progressPaint);
    if (shouldDrawOutlines && outlineRects.isNotEmpty) {
      _batchDrawRects(canvas, outlineRects, _progressOutlinePaint);
    }
  }

  /// Optimized completed zones drawing with caching and viewport culling
  void _drawCompletedZonesOptimized(
      Canvas canvas, Size size, dynamic scalingInfo, Rect visibleRect) {
    if (completedZones.isEmpty) return;

    // Create paint for completed zones using cached objects
    final completedZonePaint = Paint()
      ..color = getCompletedZoneColor()
      ..style = PaintingStyle.fill;

    final completedZoneOutlinePaint = Paint()
      ..color = getCompletedZoneOutlineColor()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Performance optimization: Calculate level of detail
    final cellSize = math.min<double>(
        scalingInfo.scaleX.toDouble(), scalingInfo.scaleY.toDouble());
    final shouldDrawOutlines = cellSize > 2.0;

    // Draw each completed zone
    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      // Skip if this zone is not completed or if it's beyond our completion tracking
      if (zoneIndex >= completedZones.length || !completedZones[zoneIndex]) {
        continue;
      }

      // Skip the current zone as it will be drawn separately
      if (zoneIndex == currentZoneIndex) continue;

      final zone = zones[zoneIndex];

      // Cache processed instructions to avoid repeated computation
      final cacheKey = '${zone.hashCode}_${zone.instructions.length}';
      List<List<bool>> processedInstructions;

      if (_processedZoneCache.containsKey(cacheKey)) {
        processedInstructions = _processedZoneCache[cacheKey]!;
      } else {
        processedInstructions =
            ZoneConfigurationProcessor.applyZoneConfiguration(zone);
        _processedZoneCache[cacheKey] = processedInstructions;
      }

      if (processedInstructions.isEmpty) continue;

      // Batch rectangles for efficient drawing
      final List<Rect> completedRects = [];
      final List<Rect> outlineRects = [];

      // Fill the entire zone with completion color
      for (int rowIndex = 0;
          rowIndex < processedInstructions.length;
          rowIndex++) {
        final fullPatternRowIndex = zone.startRow + rowIndex;

        // Viewport culling: Skip rows outside visible area
        final rowTop =
            scalingInfo.offsetY + fullPatternRowIndex * scalingInfo.scaleY;
        final rowBottom = rowTop + scalingInfo.scaleY;
        if (rowBottom < visibleRect.top || rowTop > visibleRect.bottom)
          continue;

        final row = processedInstructions[rowIndex];

        for (int colIndex = 0; colIndex < row.length; colIndex++) {
          if (row[colIndex]) {
            final centerOffset =
                (row.length - zone.instructions[rowIndex].length) ~/ 2;
            final fullPatternColIndex =
                zone.startNeedle + colIndex - centerOffset;

            // Skip rendering if outside the full pattern boundaries
            if (fullPatternColIndex < 0 ||
                fullPatternColIndex >= fullPatternInstructions[0].length) {
              continue;
            }

            // Viewport culling: Skip columns outside visible area
            final cellLeft =
                scalingInfo.offsetX + fullPatternColIndex * scalingInfo.scaleX;
            final cellRight = cellLeft + scalingInfo.scaleX;
            if (cellRight < visibleRect.left || cellLeft > visibleRect.right)
              continue;

            final cellTop =
                scalingInfo.offsetY + fullPatternRowIndex * scalingInfo.scaleY;
            final cellMargin = shouldDrawOutlines ? cellSize * 0.05 : 0.0;

            final cellRect = Rect.fromLTWH(
                cellLeft + cellMargin,
                cellTop + cellMargin,
                scalingInfo.scaleX - cellMargin * 2,
                scalingInfo.scaleY - cellMargin * 2);

            completedRects.add(cellRect);
            if (shouldDrawOutlines) {
              outlineRects.add(cellRect);
            }
          }
        }
      }

      // Batch draw all completed zone rectangles
      _batchDrawRects(canvas, completedRects, completedZonePaint);
      if (shouldDrawOutlines && outlineRects.isNotEmpty) {
        _batchDrawRects(canvas, outlineRects, completedZoneOutlinePaint);
      }

      // Draw a checkmark or "✓" symbol on completed zones
      final zoneLeft =
          scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX;
      final zoneTop = scalingInfo.offsetY + zone.startRow * scalingInfo.scaleY;
      final zoneWidth =
          (zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX;
      final zoneHeight = (zone.endRow - zone.startRow + 1) * scalingInfo.scaleY;

      _drawCompletionCheckmark(
        canvas,
        Offset(zoneLeft + zoneWidth / 2, zoneTop + zoneHeight / 2),
        math.min<double>(zoneWidth.toDouble(), zoneHeight.toDouble()) * 0.3,
      );
    }
  }

  /// Draw completion checkmark for completed zones
  void _drawCompletionCheckmark(Canvas canvas, Offset center, double size) {
    final checkPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = math.max(2.0, size * 0.1)
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw checkmark lines
    final checkSize = size * 0.7;
    final checkLeft = center - Offset(checkSize * 0.3, 0);
    final checkMiddle = center + Offset(0, checkSize * 0.2);
    final checkRight = center + Offset(checkSize * 0.4, -checkSize * 0.3);

    canvas.drawLine(checkLeft, checkMiddle, checkPaint);
    canvas.drawLine(checkMiddle, checkRight, checkPaint);

    // Add a circular background for better visibility
    final bgPaint = Paint()
      ..color = getCompletedZoneOutlineColor()
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, size * 0.6, bgPaint);

    // Redraw the checkmark on top
    canvas.drawLine(checkLeft, checkMiddle, checkPaint);
    canvas.drawLine(checkMiddle, checkRight, checkPaint);
  }

  /// Draw pattern completion overlay when entire pattern is finished
  void _drawPatternCompletionOverlay(Canvas canvas, Size size) {
    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    // Create overlay paint
    final overlayPaint = Paint()
      ..color = getPatternCompletionColor()
      ..style = PaintingStyle.fill;

    // Draw overlay over the entire pattern area
    final patternRect = Rect.fromLTWH(
      scalingInfo.offsetX,
      scalingInfo.offsetY,
      scalingInfo.scaledSize.width,
      scalingInfo.scaledSize.height,
    );

    canvas.drawRect(patternRect, overlayPaint);

    // Draw celebration border
    final celebrationPaint = Paint()
      ..color = Colors.amber
      ..strokeWidth = 4.0
      ..style = PaintingStyle.stroke;

    canvas.drawRect(patternRect, celebrationPaint);

    // Draw "COMPLETED!" text in the center
    final completionText = "knittingProgress_patternCompleted".tr;
    final textStyle = TextStyle(
      color: Colors.amber.shade800,
      fontSize: math.min(24.0, scalingInfo.scaledSize.width * 0.08),
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(text: completionText, style: textStyle);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    // Draw background for text
    final textCenter = Offset(
      scalingInfo.offsetX + scalingInfo.scaledSize.width / 2,
      scalingInfo.offsetY + scalingInfo.scaledSize.height / 2,
    );

    final textBgRect = Rect.fromCenter(
      center: textCenter,
      width: textPainter.width + 20,
      height: textPainter.height + 10,
    );

    final textBgPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(textBgRect, const Radius.circular(10)),
      textBgPaint,
    );

    // Draw the text
    textPainter.paint(
      canvas,
      Offset(
        textCenter.dx - textPainter.width / 2,
        textCenter.dy - textPainter.height / 2,
      ),
    );
  }

  /// Draw zone label
  void _drawTextInZone(Canvas canvas, String text, Offset position, Color color,
      {double fontSize = 12.0}) {
    final textStyle = TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Add a background for better readability
    final bgRect = Rect.fromCenter(
      center: position,
      width: textPainter.width + 8,
      height: textPainter.height + 4,
    );

    canvas.drawRect(
      bgRect,
      Paint()..color = getBackgroundColor().withValues(alpha: 0.8),
    );

    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  /// Draw the dimension indicators for specific rows
  void _drawDimensionIndicators(Canvas canvas, Size size) {
    if (currentZoneIndex < 0 || currentZoneIndex >= zones.length) return;

    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    final activeZone = zones[currentZoneIndex];

    // Get specific row bounds for more accurate dimension indicators
    final topRowBounds = _getZoneTopRowBounds(size, scalingInfo, activeZone);
    final bottomRowBounds =
        _getZoneBottomRowBounds(size, scalingInfo, activeZone);

    // Calculate overall zone boundaries for height dimension
    final zoneLeft =
        scalingInfo.offsetX + activeZone.startNeedle * scalingInfo.scaleX;
    final zoneTop =
        scalingInfo.offsetY + activeZone.startRow * scalingInfo.scaleY;
    final zoneHeight =
        (activeZone.endRow - activeZone.startRow + 1) * scalingInfo.scaleY;

    // Position top width dimension above the entire pattern, not just the zone
    final patternTop = scalingInfo.offsetY;
    final topDimensionY =
        patternTop - 30; // Increased from 15 to ensure clearance

    // Draw top width dimension using the actual top row bounds but positioned above the entire pattern
    _drawWidthDimension(
        canvas,
        Offset(topRowBounds.left, topDimensionY),
        Offset(topRowBounds.right, topDimensionY),
        topWidth,
        'knittingProgress_dimensionIndicator_stitches'
            .trParams({'count': topStitches.toString()}));

    // Draw bottom width dimension using the actual bottom row bounds
    _drawWidthDimension(
        canvas,
        Offset(bottomRowBounds.left, bottomRowBounds.bottom + 15),
        Offset(bottomRowBounds.right, bottomRowBounds.bottom + 15),
        bottomWidth,
        'knittingProgress_dimensionIndicator_stitches'
            .trParams({'count': bottomStitches.toString()}));

    // Draw height dimension using overall zone bounds
    _drawHeightDimension(
        canvas,
        zoneLeft - 15,
        zoneTop,
        zoneTop + zoneHeight,
        height,
        'knittingProgress_dimensionIndicator_rows'
            .trParams({'count': rowsCount.toString()}));
  }

  /// Draw width dimension
  void _drawWidthDimension(Canvas canvas, Offset start, Offset end,
      String dimension, String detail) {
    final linePaint = Paint()
      ..color = getLineColor()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw the horizontal dimension line with small vertical end markers
    canvas.drawLine(start, end, linePaint);

    // Draw the small vertical end markers
    canvas.drawLine(Offset(start.dx, start.dy - 5),
        Offset(start.dx, start.dy + 5), linePaint);
    canvas.drawLine(
        Offset(end.dx, end.dy - 5), Offset(end.dx, end.dy + 5), linePaint);

    // Draw the dimension text
    _drawTextCentered(canvas, dimension,
        Offset(start.dx + (end.dx - start.dx) / 2, start.dy - 5),
        backgroundColor: getBackgroundColor());

    // Draw the detail text
    _drawTextCentered(canvas, detail,
        Offset(start.dx + (end.dx - start.dx) / 2, start.dy + 15),
        backgroundColor: getBackgroundColor());
  }

  /// Draw height dimension with vertical text
  void _drawHeightDimension(Canvas canvas, double x, double topY,
      double bottomY, String dimension, String detail) {
    final linePaint = Paint()
      ..color = getLineColor()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw the vertical dimension line
    final lineStart = Offset(x, topY);
    final lineEnd = Offset(x, bottomY);

    // Draw the main vertical line
    canvas.drawLine(lineStart, lineEnd, linePaint);

    // Draw the small horizontal end markers
    canvas.drawLine(Offset(lineStart.dx - 5, lineStart.dy),
        Offset(lineStart.dx + 5, lineStart.dy), linePaint);
    canvas.drawLine(Offset(lineEnd.dx - 5, lineEnd.dy),
        Offset(lineEnd.dx + 5, lineEnd.dy), linePaint);

    // Draw the dimension text vertically
    canvas.save();

    // Translate to the center of the vertical line, offset to the left
    canvas.translate(
        lineStart.dx - 15, lineStart.dy + (lineEnd.dy - lineStart.dy) / 2);

    // Rotate -90 degrees to make text vertical (reading from bottom to top)
    canvas.rotate(-math.pi / 2);

    // Draw the detail text (number of rows) first, positioned at the top
    _drawTextCentered(canvas, detail, Offset(0, 15),
        backgroundColor: getBackgroundColor());

    // Draw the dimension text (height in cm) below the rows text
    _drawTextCentered(canvas, dimension, Offset(25, 0),
        backgroundColor: getBackgroundColor());

    canvas.restore();
  }

  /// Draw text centered at a position
  void _drawTextCentered(Canvas canvas, String text, Offset position,
      {Color? backgroundColor}) {
    final textStyle = TextStyle(
      color: getTextColor(),
      fontSize: 12,
      fontWeight: FontWeight.w500,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    if (backgroundColor != null && backgroundColor != Colors.transparent) {
      final backgroundRect = Rect.fromCenter(
        center: position,
        width: textPainter.width + 10,
        height: textPainter.height + 4,
      );

      canvas.drawRect(
        backgroundRect,
        Paint()..color = backgroundColor,
      );
    }

    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  /// Draw progress percentage in top-right corner
  void _drawProgressPercentageInTopRight(Canvas canvas, Size size) {
    final percentValue = (progressPercentage * 100).round();
    final String text = Get.locale?.languageCode == 'en'
        ? '$percentValue% Complete'
        : 'knittingProgress_percentComplete'.trParams({
            'percent': percentValue.toString(),
          });

    final textStyle = TextStyle(
      color: getPrimaryColor(),
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Position in the top-right corner
    final position = Offset(
      size.width - textPainter.width - 10,
      10,
    );

    // Draw a faint background for better readability
    final backgroundPaint = Paint()
      ..color = getBackgroundColor().withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;

    final bgRect = RRect.fromLTRBR(
        position.dx - 4,
        position.dy - 2,
        position.dx + textPainter.width + 4,
        position.dy + textPainter.height + 2,
        const Radius.circular(4));

    canvas.drawRRect(bgRect, backgroundPaint);
    textPainter.paint(canvas, position);
  }

  /// Draw current row indicator
  void _drawCurrentRowIndicator(Canvas canvas, Size size) {
    if (currentZoneIndex < 0 || currentZoneIndex >= zones.length) return;

    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    final activeZone = zones[currentZoneIndex];

    // Calculate zone boundaries
    final zoneLeft =
        scalingInfo.offsetX + activeZone.startNeedle * scalingInfo.scaleX;
    final zoneTop =
        scalingInfo.offsetY + activeZone.startRow * scalingInfo.scaleY;
    final zoneWidth = (activeZone.endNeedle - activeZone.startNeedle + 1) *
        scalingInfo.scaleX;
    final zoneHeight =
        (activeZone.endRow - activeZone.startRow + 1) * scalingInfo.scaleY;

    // Calculate position based on progress
    final progressY = zoneTop + zoneHeight - (zoneHeight * progressPercentage);

    // Position on the right side of the shape
    final rightEdge = zoneLeft + zoneWidth + 90;

    // Get the user-facing row number - ensure it matches interactive knitting view calculation
    final totalRows = activeZone.instructions.length;
    final userFacingRowNum = totalRows - currentRow;

    // Draw the row text
    final rowText = 'knittingProgress_row'
        .trParams({'number': userFacingRowNum.toString()});

    _drawRightAlignedText(canvas, rowText, Offset(rightEdge, progressY));

    // Draw the needle range using absolute needle numbers to match knitting instructions
    String needleRangeText = 'No active needles';

    // First priority: Use absolute needle numbers from zonePositionInfo (matches knitting instructions)
    if (zonePositionInfo != null) {
      final startNeedleLabel = zonePositionInfo!['startNeedleLabel'] as String?;
      final endNeedleLabel = zonePositionInfo!['endNeedleLabel'] as String?;

      if (startNeedleLabel != null &&
          endNeedleLabel != null &&
          startNeedleLabel.isNotEmpty &&
          endNeedleLabel.isNotEmpty) {
        // Display the actual needle range like "L5 to R10" to match knitting instructions exactly
        if (startNeedleLabel == endNeedleLabel) {
          needleRangeText = startNeedleLabel;
        } else {
          needleRangeText = '$startNeedleLabel to $endNeedleLabel';
        }
      }
    }

    // Fallback: Use needle counts only if absolute numbers are not available
    if (needleRangeText == 'No active needles' &&
        (leftNeedles > 0 || rightNeedles > 0)) {
      if (leftNeedles > 0 && rightNeedles > 0) {
        needleRangeText = 'L${leftNeedles} to R${rightNeedles}';
      } else if (leftNeedles > 0) {
        needleRangeText = leftNeedles > 1 ? 'L1 to L${leftNeedles}' : 'L1';
      } else {
        needleRangeText = rightNeedles > 1 ? 'R1 to R${rightNeedles}' : 'R1';
      }
    }

    _drawRightAlignedText(
        canvas, needleRangeText, Offset(rightEdge, progressY + 15));
  }

  /// Draw horizontal indicator line for current row
  void _drawHorizontalRowLine(Canvas canvas, Size size) {
    if (currentZoneIndex < 0 || currentZoneIndex >= zones.length) return;

    // Nearly zero padding to maximize pattern size
    const double padding =
        0.1; // Nearly zero padding for maximum space utilization

    // Calculate available size for drawing
    final availableWidth = math.max(0, size.width - 2 * padding);
    final availableHeight = math.max(0, size.height - 2 * padding);
    final scalingInfo = _calculateScalingInfo(
        Size(availableWidth.toDouble(), availableHeight.toDouble()));

    final activeZone = zones[currentZoneIndex];

    // Calculate zone boundaries
    final zoneLeft =
        scalingInfo.offsetX + activeZone.startNeedle * scalingInfo.scaleX;
    final zoneTop =
        scalingInfo.offsetY + activeZone.startRow * scalingInfo.scaleY;
    final zoneWidth = (activeZone.endNeedle - activeZone.startNeedle + 1) *
        scalingInfo.scaleX;
    final zoneHeight =
        (activeZone.endRow - activeZone.startRow + 1) * scalingInfo.scaleY;

    // Calculate row position based on progress
    final progressY = zoneTop + zoneHeight - (zoneHeight * progressPercentage);

    // Create a stronger, more visible dashed line
    final dashPaint = Paint()
      ..color = getPrimaryColor()
      ..strokeWidth = 2.0 // Increased from 1.5
      ..style = PaintingStyle.stroke;

    // Extend the line beyond the zone edges
    final leftmostX = zoneLeft - 15;
    final rightmostX = zoneLeft + zoneWidth + 35;

    // Draw a semi-transparent background rectangle to highlight current row
    final rowHighlightPaint = Paint()
      ..color = getPrimaryColor().withValues(alpha: 0.15)
      ..style = PaintingStyle.fill;

    final rowHighlightHeight = math.max(scalingInfo.scaleY, 5.0);
    final rowHighlightRect = Rect.fromLTWH(
        leftmostX,
        progressY - rowHighlightHeight / 2,
        rightmostX - leftmostX,
        rowHighlightHeight);

    canvas.drawRect(rowHighlightRect, rowHighlightPaint);

    // Draw dashed line
    final dashWidth = 5.0;
    final dashSpace = 3.0;
    double distance = rightmostX - leftmostX;
    double drawn = 0;

    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(leftmostX + drawn, progressY),
        Offset(leftmostX + drawn + dashLength, progressY),
        dashPaint,
      );
      drawn += dashLength + dashSpace;
    }

    // Draw larger circle indicators at the ends
    final circlePaint = Paint()
      ..color = getPrimaryColor()
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
        Offset(leftmostX, progressY), 4, circlePaint); // Increased from 3
    canvas.drawCircle(
        Offset(rightmostX, progressY), 4, circlePaint); // Increased from 3
  }

  /// Draw right aligned text
  void _drawRightAlignedText(Canvas canvas, String text, Offset position) {
    final textStyle = TextStyle(
      color: getTextColor(),
      fontSize: 12,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  /// Get bounds for just the top-most row of the current zone
  Rect _getZoneTopRowBounds(Size size, dynamic scalingInfo, KnittingZone zone) {
    if (zone.instructions.isEmpty) {
      // Fall back to zone bounds if no instructions
      final zoneLeft =
          (scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX)
              .toDouble();
      final zoneTop =
          (scalingInfo.offsetY + zone.startRow * scalingInfo.scaleY).toDouble();
      final zoneWidth =
          ((zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX)
              .toDouble();
      return Rect.fromLTWH(
          zoneLeft, zoneTop, zoneWidth, scalingInfo.scaleY.toDouble());
    }

    // Get the first row (top-most) of the zone
    final topRow = zone.instructions.first;
    int topLeftStitch = -1;
    int topRightStitch = -1;

    // Find leftmost and rightmost stitches in the top row only
    for (int j = 0; j < topRow.length; j++) {
      if (topRow[j]) {
        if (topLeftStitch < 0) topLeftStitch = j;
        topRightStitch = j;
      }
    }

    // If no stitches in top row, use zone boundaries as fallback
    if (topLeftStitch < 0) {
      final zoneLeft =
          (scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX)
              .toDouble();
      final zoneTop =
          (scalingInfo.offsetY + zone.startRow * scalingInfo.scaleY).toDouble();
      final zoneWidth =
          ((zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX)
              .toDouble();
      return Rect.fromLTWH(
          zoneLeft, zoneTop, zoneWidth, scalingInfo.scaleY.toDouble());
    }

    // Convert zone-local coordinates to global pattern coordinates
    final globalLeftNeedle = zone.startNeedle + topLeftStitch;
    final globalRightNeedle = zone.startNeedle + topRightStitch;
    final globalTopRow = zone.startRow;

    final topY =
        (scalingInfo.offsetY + globalTopRow * scalingInfo.scaleY).toDouble();
    final leftX = (scalingInfo.offsetX + globalLeftNeedle * scalingInfo.scaleX)
        .toDouble();
    final rightX =
        (scalingInfo.offsetX + (globalRightNeedle + 1) * scalingInfo.scaleX)
            .toDouble();

    return Rect.fromLTRB(
        leftX, topY, rightX, (topY + scalingInfo.scaleY).toDouble());
  }

  /// Get bounds for just the bottom-most row of the current zone
  Rect _getZoneBottomRowBounds(
      Size size, dynamic scalingInfo, KnittingZone zone) {
    if (zone.instructions.isEmpty) {
      // Fall back to zone bounds if no instructions
      final zoneLeft =
          (scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX)
              .toDouble();
      final zoneBottom =
          (scalingInfo.offsetY + zone.endRow * scalingInfo.scaleY).toDouble();
      final zoneWidth =
          ((zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX)
              .toDouble();
      return Rect.fromLTWH(
          zoneLeft, zoneBottom, zoneWidth, scalingInfo.scaleY.toDouble());
    }

    // Get the last row (bottom-most) of the zone
    final bottomRow = zone.instructions.last;
    int bottomLeftStitch = -1;
    int bottomRightStitch = -1;

    // Find leftmost and rightmost stitches in the bottom row only
    for (int j = 0; j < bottomRow.length; j++) {
      if (bottomRow[j]) {
        if (bottomLeftStitch < 0) bottomLeftStitch = j;
        bottomRightStitch = j;
      }
    }

    // If no stitches in bottom row, use zone boundaries as fallback
    if (bottomLeftStitch < 0) {
      final zoneLeft =
          (scalingInfo.offsetX + zone.startNeedle * scalingInfo.scaleX)
              .toDouble();
      final zoneBottom =
          (scalingInfo.offsetY + zone.endRow * scalingInfo.scaleY).toDouble();
      final zoneWidth =
          ((zone.endNeedle - zone.startNeedle + 1) * scalingInfo.scaleX)
              .toDouble();
      return Rect.fromLTWH(
          zoneLeft, zoneBottom, zoneWidth, scalingInfo.scaleY.toDouble());
    }

    // Convert zone-local coordinates to global pattern coordinates
    final globalLeftNeedle = zone.startNeedle + bottomLeftStitch;
    final globalRightNeedle = zone.startNeedle + bottomRightStitch;
    final globalBottomRow = zone.endRow;

    final bottomY =
        (scalingInfo.offsetY + globalBottomRow * scalingInfo.scaleY).toDouble();
    final leftX = (scalingInfo.offsetX + globalLeftNeedle * scalingInfo.scaleX)
        .toDouble();
    final rightX =
        (scalingInfo.offsetX + (globalRightNeedle + 1) * scalingInfo.scaleX)
            .toDouble();

    return Rect.fromLTRB(
        leftX, bottomY, rightX, (bottomY + scalingInfo.scaleY).toDouble());
  }

  /// Draw a dashed rectangle
  void _drawDashedRect(Canvas canvas, Rect rect, Paint paint) {
    final dashWidth = 5.0;
    final dashSpace = 5.0;

    // Top edge
    double distance = rect.width;
    double drawn = 0;
    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(rect.left + drawn, rect.top),
        Offset(rect.left + drawn + dashLength, rect.top),
        paint,
      );
      drawn += dashLength + dashSpace;
    }

    // Right edge
    distance = rect.height;
    drawn = 0;
    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(rect.right, rect.top + drawn),
        Offset(rect.right, rect.top + drawn + dashLength),
        paint,
      );
      drawn += dashLength + dashSpace;
    }

    // Bottom edge
    distance = rect.width;
    drawn = 0;
    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(rect.right - drawn, rect.bottom),
        Offset(rect.right - drawn - dashLength, rect.bottom),
        paint,
      );
      drawn += dashLength + dashSpace;
    }

    // Left edge
    distance = rect.height;
    drawn = 0;
    while (drawn < distance) {
      final dashLength = math.min(dashWidth, distance - drawn);
      canvas.drawLine(
        Offset(rect.left, rect.bottom - drawn),
        Offset(rect.left, rect.bottom - drawn - dashLength),
        paint,
      );
      drawn += dashLength + dashSpace;
    }
  }

  /// Clear caches when pattern data changes significantly
  static void clearCaches() {
    _patternCache.clear();
    _processedZoneCache.clear();
  }

  /// Manage cache size to prevent memory issues
  static void _manageCacheSize() {
    // Keep cache size reasonable - remove oldest entries if too large
    const maxCacheSize = 50;

    if (_processedZoneCache.length > maxCacheSize) {
      final keysToRemove = _processedZoneCache.keys
          .take(_processedZoneCache.length - maxCacheSize);
      for (final key in keysToRemove) {
        _processedZoneCache.remove(key);
      }
    }

    if (_patternCache.length > maxCacheSize) {
      final keysToRemove =
          _patternCache.keys.take(_patternCache.length - maxCacheSize);
      for (final key in keysToRemove) {
        _patternCache.remove(key);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is ZoneAwareProgressPainter) {
      final shouldRepaint = oldDelegate.currentZoneIndex != currentZoneIndex ||
          oldDelegate.currentRow != currentRow ||
          oldDelegate.progressPercentage != progressPercentage ||
          oldDelegate.zones != zones ||
          oldDelegate.fullPatternInstructions != fullPatternInstructions ||
          oldDelegate.completedZones != completedZones ||
          oldDelegate.isPatternCompleted != isPatternCompleted;

      // Clear caches if pattern data has changed
      if (shouldRepaint &&
          (oldDelegate.zones != zones ||
              oldDelegate.fullPatternInstructions != fullPatternInstructions)) {
        clearCaches();
      }

      // Manage cache size periodically
      _manageCacheSize();

      return shouldRepaint;
    }
    return true;
  }
}
